#!/usr/bin/env python3
"""
ELOPE Baseline Method Implementation
Simple baseline approach for velocity estimation from event streams
"""

import numpy as np
import pandas as pd
import json
import os
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from scipy import interpolate
from scipy.spatial.transform import Rotation as R
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class ELOPEBaseline:
    """Baseline method for ELOPE velocity estimation"""
    
    def __init__(self, data_path: str = "."):
        self.data_path = Path(data_path)
        self.train_path = self.data_path / "train"
        self.test_path = self.data_path / "test"
        
        # Get sequence lists
        self.train_sequences = sorted([f.stem for f in self.train_path.glob("*.npz")])
        self.test_sequences = sorted([f.stem for f in self.test_path.glob("*.npz")])
        
        print(f"Baseline method initialized with {len(self.train_sequences)} train, {len(self.test_sequences)} test sequences")
        
    def load_sequence(self, sequence_id: str, is_test: bool = False) -> Dict:
        """Load a single sequence"""
        path = self.test_path if is_test else self.train_path
        file_path = path / f"{sequence_id}.npz"
        
        data = np.load(file_path)
        return {
            'events': data['events'],
            'timestamps': data['timestamps'],
            'traj': data['traj'],
            'range_meter': data['range_meter']
        }
    
    def extract_event_features(self, events: np.ndarray, timestamps: np.ndarray) -> np.ndarray:
        """Extract features from event stream for each timestamp"""
        features = []
        
        # Convert events to DataFrame for easier processing
        df = pd.DataFrame(events)
        
        # Convert timestamps to microseconds for comparison with events
        timestamps_us = timestamps * 1e6
        
        for i, t in enumerate(timestamps_us):
            # Define time window around each timestamp (±0.1 seconds)
            window = 100000  # 0.1 seconds in microseconds
            t_start = t - window
            t_end = t + window
            
            # Get events in this time window
            mask = (df['t'] >= t_start) & (df['t'] <= t_end)
            window_events = df[mask]
            
            if len(window_events) == 0:
                # No events in window, use zeros
                feat = np.zeros(12)
            else:
                # Extract basic statistical features
                feat = np.array([
                    len(window_events),  # Total event count
                    np.sum(window_events['p']),  # Positive events
                    np.sum(~window_events['p']),  # Negative events
                    np.mean(window_events['x']),  # Mean x position
                    np.mean(window_events['y']),  # Mean y position
                    np.std(window_events['x']),   # Std x position
                    np.std(window_events['y']),   # Std y position
                    np.max(window_events['x']) - np.min(window_events['x']),  # X range
                    np.max(window_events['y']) - np.min(window_events['y']),  # Y range
                    len(np.unique(window_events['x'])),  # Unique x positions
                    len(np.unique(window_events['y'])),  # Unique y positions
                    len(window_events) / (2 * window / 1e6)  # Event rate (Hz)
                ])
            
            features.append(feat)
        
        return np.array(features)
    
    def extract_imu_features(self, traj: np.ndarray) -> np.ndarray:
        """Extract IMU-based features from trajectory"""
        # Extract orientation and angular rates
        orientations = traj[:, 6:9]  # phi, theta, psi
        angular_rates = traj[:, 9:12]  # p, q, r
        
        # Calculate additional features
        angular_speed = np.linalg.norm(angular_rates, axis=1)
        
        # Combine features
        imu_features = np.column_stack([
            orientations,
            angular_rates,
            angular_speed
        ])
        
        return imu_features
    
    def extract_range_features(self, range_meter: np.ndarray, timestamps: np.ndarray) -> np.ndarray:
        """Extract range meter features interpolated to trajectory timestamps"""
        range_times = range_meter[:, 0]
        range_distances = range_meter[:, 1]
        
        # Interpolate range meter to trajectory timestamps
        interp_func = interpolate.interp1d(range_times, range_distances, 
                                         kind='linear', bounds_error=False, fill_value='extrapolate')
        range_at_timestamps = interp_func(timestamps)
        
        # Calculate range rate (numerical derivative)
        dt = np.diff(timestamps)
        range_rate = np.gradient(range_at_timestamps, timestamps)
        
        # Combine features
        range_features = np.column_stack([
            range_at_timestamps,
            range_rate
        ])
        
        return range_features
    
    def extract_all_features(self, sequence_data: Dict) -> np.ndarray:
        """Extract all features for a sequence"""
        events = sequence_data['events']
        timestamps = sequence_data['timestamps']
        traj = sequence_data['traj']
        range_meter = sequence_data['range_meter']
        
        # Extract different feature types
        event_features = self.extract_event_features(events, timestamps)
        imu_features = self.extract_imu_features(traj)
        range_features = self.extract_range_features(range_meter, timestamps)
        
        # Combine all features
        all_features = np.column_stack([
            event_features,
            imu_features,
            range_features
        ])
        
        return all_features
    
    def train_velocity_model(self) -> Dict:
        """Train velocity estimation models using training data"""
        print("Training velocity estimation models...")
        
        all_features = []
        all_velocities = []
        
        # Collect training data
        for seq_id in self.train_sequences:
            try:
                data = self.load_sequence(seq_id)
                features = self.extract_all_features(data)
                velocities = data['traj'][:, 3:6]  # vx, vy, vz
                
                all_features.append(features)
                all_velocities.append(velocities)
                
            except Exception as e:
                print(f"Error processing training sequence {seq_id}: {e}")
                continue
        
        # Combine all training data
        X_train = np.vstack(all_features)
        y_train = np.vstack(all_velocities)
        
        print(f"Training data shape: {X_train.shape}, {y_train.shape}")
        
        # Handle NaN values
        X_train = np.nan_to_num(X_train, nan=0.0, posinf=0.0, neginf=0.0)
        y_train = np.nan_to_num(y_train, nan=0.0, posinf=0.0, neginf=0.0)
        
        # Scale features
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        
        # Train separate models for each velocity component
        self.models = {}
        for i, component in enumerate(['vx', 'vy', 'vz']):
            model = LinearRegression()
            model.fit(X_train_scaled, y_train[:, i])
            self.models[component] = model
            
            # Calculate training score
            score = model.score(X_train_scaled, y_train[:, i])
            print(f"Training R² for {component}: {score:.4f}")
        
        return self.models
    
    def predict_velocity(self, sequence_data: Dict) -> np.ndarray:
        """Predict velocity for a sequence"""
        features = self.extract_all_features(sequence_data)
        
        # Handle NaN values
        features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
        
        # Scale features
        features_scaled = self.scaler.transform(features)
        
        # Predict each velocity component
        velocities = np.zeros((len(features), 3))
        for i, component in enumerate(['vx', 'vy', 'vz']):
            velocities[:, i] = self.models[component].predict(features_scaled)
        
        return velocities
    
    def generate_submission(self, output_file: str = "baseline_submission.json"):
        """Generate submission file for test sequences"""
        print("Generating submission file...")
        
        submission = {}
        
        for seq_id in self.test_sequences:
            try:
                # Load test sequence
                data = self.load_sequence(seq_id, is_test=True)
                
                # Predict velocities
                predicted_velocities = self.predict_velocity(data)
                
                # Format for submission
                submission[seq_id] = {
                    "vx": predicted_velocities[:, 0].tolist(),
                    "vy": predicted_velocities[:, 1].tolist(),
                    "vz": predicted_velocities[:, 2].tolist()
                }
                
                print(f"Processed test sequence {seq_id}")
                
            except Exception as e:
                print(f"Error processing test sequence {seq_id}: {e}")
                continue
        
        # Save submission
        with open(output_file, 'w') as f:
            json.dump(submission, f, indent=2)
        
        print(f"Submission saved to {output_file}")
        print(f"Processed {len(submission)} test sequences")
        
        return submission
    
    def evaluate_on_training(self) -> Dict:
        """Evaluate model performance on training data"""
        print("Evaluating on training data...")
        
        errors = []
        
        for seq_id in self.train_sequences[:5]:  # Evaluate on first 5 sequences
            try:
                data = self.load_sequence(seq_id)
                true_velocities = data['traj'][:, 3:6]
                predicted_velocities = self.predict_velocity(data)
                
                # Calculate RMSE for each component
                rmse = np.sqrt(np.mean((predicted_velocities - true_velocities)**2, axis=0))
                errors.append(rmse)
                
                print(f"Sequence {seq_id} RMSE: vx={rmse[0]:.2f}, vy={rmse[1]:.2f}, vz={rmse[2]:.2f}")
                
            except Exception as e:
                print(f"Error evaluating sequence {seq_id}: {e}")
                continue
        
        if errors:
            mean_rmse = np.mean(errors, axis=0)
            print(f"Mean RMSE: vx={mean_rmse[0]:.2f}, vy={mean_rmse[1]:.2f}, vz={mean_rmse[2]:.2f}")
            
            return {
                'mean_rmse': mean_rmse.tolist(),
                'individual_rmse': [e.tolist() for e in errors]
            }
        
        return {}

def main():
    """Main execution function"""
    print("=== ELOPE Baseline Method ===")
    
    # Initialize baseline method
    baseline = ELOPEBaseline()
    
    # Train models
    models = baseline.train_velocity_model()
    
    # Evaluate on training data
    eval_results = baseline.evaluate_on_training()
    
    # Generate submission
    submission = baseline.generate_submission()
    
    print("\nBaseline method complete!")
    print("Next steps: Implement more sophisticated algorithms to improve performance")

if __name__ == "__main__":
    main()
