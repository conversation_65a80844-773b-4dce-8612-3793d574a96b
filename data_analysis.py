#!/usr/bin/env python3
"""
ELOPE Data Analysis Script
Deep analysis of event streams, trajectories, and sensor data
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import os
from pathlib import Path
import json
from typing import Dict, List, Tuple, Optional
import seaborn as sns
from scipy import interpolate
from scipy.spatial.transform import Rotation as R

class ELOPEDataAnalyzer:
    """Comprehensive data analyzer for ELOPE dataset"""
    
    def __init__(self, data_path: str = "."):
        self.data_path = Path(data_path)
        self.train_path = self.data_path / "train"
        self.test_path = self.data_path / "test"
        
        # Get sequence lists
        self.train_sequences = sorted([f.stem for f in self.train_path.glob("*.npz")])
        self.test_sequences = sorted([f.stem for f in self.test_path.glob("*.npz")])
        
        print(f"Found {len(self.train_sequences)} training sequences")
        print(f"Found {len(self.test_sequences)} test sequences")
        
    def load_sequence(self, sequence_id: str, is_test: bool = False) -> Dict:
        """Load a single sequence"""
        path = self.test_path if is_test else self.train_path
        file_path = path / f"{sequence_id}.npz"
        
        if not file_path.exists():
            raise FileNotFoundError(f"Sequence {sequence_id} not found")
            
        data = np.load(file_path)
        return {
            'events': data['events'],
            'timestamps': data['timestamps'],
            'traj': data['traj'],
            'range_meter': data['range_meter']
        }
    
    def analyze_events_statistics(self, sequence_id: str = "0000") -> Dict:
        """Analyze event stream statistics"""
        data = self.load_sequence(sequence_id)
        events = data['events']
        
        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(events)
        
        stats = {
            'total_events': len(events),
            'duration_us': events['t'][-1] - events['t'][0],
            'event_rate_hz': len(events) / ((events['t'][-1] - events['t'][0]) / 1e6),
            'positive_events': np.sum(events['p']),
            'negative_events': np.sum(~events['p']),
            'polarity_ratio': np.sum(events['p']) / len(events),
            'spatial_coverage': {
                'x_range': (events['x'].min(), events['x'].max()),
                'y_range': (events['y'].min(), events['y'].max()),
                'unique_pixels': len(np.unique(np.column_stack([events['x'], events['y']]), axis=0))
            }
        }
        
        return stats
    
    def analyze_trajectory_patterns(self, sequence_id: str = "0000") -> Dict:
        """Analyze trajectory and motion patterns"""
        data = self.load_sequence(sequence_id)
        traj = data['traj']
        timestamps = data['timestamps']
        
        # Extract components
        positions = traj[:, :3]  # x, y, z
        velocities = traj[:, 3:6]  # vx, vy, vz
        orientations = traj[:, 6:9]  # phi, theta, psi
        angular_rates = traj[:, 9:12]  # p, q, r
        
        # Calculate derived quantities
        speeds = np.linalg.norm(velocities, axis=1)
        altitudes = -positions[:, 2]  # z is negative by convention
        
        # Acceleration (numerical derivative)
        dt = np.diff(timestamps)
        accelerations = np.diff(velocities, axis=0) / dt[:, np.newaxis]
        
        stats = {
            'duration_s': timestamps[-1] - timestamps[0],
            'altitude_range': (altitudes.min(), altitudes.max()),
            'speed_range': (speeds.min(), speeds.max()),
            'max_acceleration': np.max(np.linalg.norm(accelerations, axis=1)),
            'trajectory_length': np.sum(np.linalg.norm(np.diff(positions, axis=0), axis=1)),
            'angular_motion': {
                'max_angular_rate': np.max(np.linalg.norm(angular_rates, axis=1)),
                'orientation_change': np.max(orientations, axis=0) - np.min(orientations, axis=0)
            }
        }
        
        return stats
    
    def analyze_sensor_correlation(self, sequence_id: str = "0000") -> Dict:
        """Analyze correlation between different sensors"""
        data = self.load_sequence(sequence_id)
        
        # Interpolate range meter to trajectory timestamps
        range_times = data['range_meter'][:, 0]
        range_distances = data['range_meter'][:, 1]
        traj_times = data['timestamps']
        
        # Interpolate range meter data
        interp_func = interpolate.interp1d(range_times, range_distances, 
                                         kind='linear', bounds_error=False, fill_value='extrapolate')
        range_at_traj = interp_func(traj_times)
        
        # Compare with altitude
        altitudes = -data['traj'][:, 2]
        
        # Calculate correlation
        correlation = np.corrcoef(range_at_traj, altitudes)[0, 1]
        
        return {
            'range_altitude_correlation': correlation,
            'range_mean_diff': np.mean(np.abs(range_at_traj - altitudes)),
            'range_std_diff': np.std(range_at_traj - altitudes)
        }
    
    def visualize_sequence_overview(self, sequence_id: str = "0000", save_path: Optional[str] = None):
        """Create comprehensive visualization of a sequence"""
        data = self.load_sequence(sequence_id)
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'Sequence {sequence_id} Overview', fontsize=16)
        
        # 1. Event frame visualization
        events = data['events']
        df = pd.DataFrame(events)
        
        # Create event frame for middle time period
        mid_time = (events['t'][0] + events['t'][-1]) // 2
        time_window = 100000  # 0.1 seconds
        
        mask = (df['t'] >= mid_time - time_window//2) & (df['t'] <= mid_time + time_window//2)
        events_subset = df[mask]
        
        # Plot event frame
        ax = axes[0, 0]
        pos_events = events_subset[events_subset['p'] == True]
        neg_events = events_subset[events_subset['p'] == False]
        
        ax.scatter(pos_events['x'], pos_events['y'], c='red', s=1, alpha=0.6, label='Positive')
        ax.scatter(neg_events['x'], neg_events['y'], c='blue', s=1, alpha=0.6, label='Negative')
        ax.set_xlim(0, 200)
        ax.set_ylim(0, 200)
        ax.set_title('Event Frame (Mid-sequence)')
        ax.legend()
        
        # 2. Trajectory 3D
        ax = axes[0, 1]
        traj = data['traj']
        ax.plot(traj[:, 0], traj[:, 1], 'b-', linewidth=2)
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_title('Trajectory (X-Y plane)')
        ax.grid(True)
        
        # 3. Altitude vs time
        ax = axes[0, 2]
        timestamps = data['timestamps']
        altitudes = -traj[:, 2]
        ax.plot(timestamps, altitudes, 'g-', linewidth=2, label='Altitude')
        
        # Add range meter data
        range_data = data['range_meter']
        ax.plot(range_data[:, 0], range_data[:, 1], 'r--', alpha=0.7, label='Range meter')
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('Distance (m)')
        ax.set_title('Altitude vs Range meter')
        ax.legend()
        ax.grid(True)
        
        # 4. Velocity components
        ax = axes[1, 0]
        velocities = traj[:, 3:6]
        ax.plot(timestamps, velocities[:, 0], 'r-', label='vx')
        ax.plot(timestamps, velocities[:, 1], 'g-', label='vy')
        ax.plot(timestamps, velocities[:, 2], 'b-', label='vz')
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('Velocity (m/s)')
        ax.set_title('Velocity Components')
        ax.legend()
        ax.grid(True)
        
        # 5. Event rate over time
        ax = axes[1, 1]
        time_bins = np.linspace(events['t'][0], events['t'][-1], 50)
        event_counts, _ = np.histogram(events['t'], bins=time_bins)
        bin_centers = (time_bins[:-1] + time_bins[1:]) / 2
        bin_widths = np.diff(time_bins)
        event_rates = event_counts / (bin_widths / 1e6)  # events per second
        
        ax.plot(bin_centers / 1e6, event_rates, 'purple', linewidth=2)
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('Event Rate (Hz)')
        ax.set_title('Event Rate Over Time')
        ax.grid(True)
        
        # 6. Angular motion
        ax = axes[1, 2]
        angular_rates = traj[:, 9:12]
        ax.plot(timestamps, angular_rates[:, 0], 'r-', label='p (roll rate)')
        ax.plot(timestamps, angular_rates[:, 1], 'g-', label='q (pitch rate)')
        ax.plot(timestamps, angular_rates[:, 2], 'b-', label='r (yaw rate)')
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('Angular Rate (rad/s)')
        ax.set_title('Angular Rates')
        ax.legend()
        ax.grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def compare_sequences(self, sequence_ids: List[str] = None) -> Dict:
        """Compare statistics across multiple sequences"""
        if sequence_ids is None:
            sequence_ids = self.train_sequences[:5]  # First 5 sequences
        
        comparison = {}
        
        for seq_id in sequence_ids:
            try:
                event_stats = self.analyze_events_statistics(seq_id)
                traj_stats = self.analyze_trajectory_patterns(seq_id)
                sensor_stats = self.analyze_sensor_correlation(seq_id)
                
                comparison[seq_id] = {
                    'events': event_stats,
                    'trajectory': traj_stats,
                    'sensors': sensor_stats
                }
            except Exception as e:
                print(f"Error analyzing sequence {seq_id}: {e}")
        
        return comparison

def main():
    """Main analysis function"""
    analyzer = ELOPEDataAnalyzer()
    
    print("=== ELOPE Data Analysis ===")
    
    # Analyze first sequence in detail
    print("\n1. Analyzing sequence 0000...")
    event_stats = analyzer.analyze_events_statistics("0000")
    print("Event Statistics:")
    for key, value in event_stats.items():
        print(f"  {key}: {value}")
    
    traj_stats = analyzer.analyze_trajectory_patterns("0000")
    print("\nTrajectory Statistics:")
    for key, value in traj_stats.items():
        print(f"  {key}: {value}")
    
    sensor_stats = analyzer.analyze_sensor_correlation("0000")
    print("\nSensor Correlation:")
    for key, value in sensor_stats.items():
        print(f"  {key}: {value}")
    
    # Create visualization
    print("\n2. Creating visualization...")
    analyzer.visualize_sequence_overview("0000", "sequence_0000_analysis.png")
    
    # Compare multiple sequences
    print("\n3. Comparing sequences...")
    comparison = analyzer.compare_sequences()
    
    # Save analysis results
    with open("data_analysis_results.json", "w") as f:
        json.dump(comparison, f, indent=2, default=str)
    
    print("Analysis complete! Results saved to data_analysis_results.json")

if __name__ == "__main__":
    main()
